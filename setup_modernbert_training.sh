#!/bin/bash

# FG-PRM ModernBERT Complete Setup Script
# This script sets up the entire environment for ModernBERT-based fine-grained reward modeling

set -e

echo "=========================================="
echo "FG-PRM ModernBERT Complete Setup Script"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Step 1: Install dependencies
print_header "Installing dependencies..."
./install_modernbert_dependencies.sh

# Step 2: Verify project structure
print_header "Verifying project structure..."
if [ ! -d "src" ]; then
    print_error "src directory not found. Please run this script from the project root."
    exit 1
fi

if [ ! -d "data" ]; then
    print_error "data directory not found. Please ensure training data is available."
    exit 1
fi

# Step 3: Create necessary directories
print_header "Creating necessary directories..."
mkdir -p models
mkdir -p logs
mkdir -p results

# Step 4: Set up Python path
print_header "Setting up Python path..."
export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"
echo "export PYTHONPATH=\"\${PYTHONPATH}:$(pwd)/src\"" >> ~/.bashrc

# Step 5: Test ModernBERT installation
print_header "Testing ModernBERT installation..."
cd src
python3 -c "
import sys
sys.path.append('.')
try:
    from my_modernbert import ModernBertForTokenClassification
    from transformers import AutoTokenizer, AutoConfig
    
    print('✓ ModernBERT model classes imported successfully')
    
    # Test tokenizer loading
    tokenizer = AutoTokenizer.from_pretrained('answerdotai/ModernBERT-base')
    print('✓ ModernBERT tokenizer loaded successfully')
    
    # Test config loading
    config = AutoConfig.from_pretrained('answerdotai/ModernBERT-base')
    config.num_labels = 3  # O, ERR, Ignore
    print('✓ ModernBERT config loaded successfully')
    
    print('✓ All ModernBERT components are working correctly')
    
except Exception as e:
    print(f'✗ Error testing ModernBERT: {e}')
    sys.exit(1)
"

if [ $? -ne 0 ]; then
    print_error "ModernBERT installation test failed"
    exit 1
fi

# Step 6: Check training data
print_header "Checking training data..."
if [ -f "../data/hallucination_sample/synthetic_sixinone_train.json" ]; then
    print_status "Training data found"
else
    print_warning "Training data not found at ../data/hallucination_sample/synthetic_sixinone_train.json"
    print_warning "Please ensure training data is available before starting training"
fi

# Step 7: Test training script syntax
print_header "Testing training script syntax..."
python3 -m py_compile reward_modeling/run_fg_rm_six_modernbert.py
if [ $? -eq 0 ]; then
    print_status "Training script syntax is valid"
else
    print_error "Training script has syntax errors"
    exit 1
fi

# Step 8: Create launch script
print_header "Creating launch script..."
cat > ../launch_modernbert_training.sh << 'EOF'
#!/bin/bash

# Launch script for ModernBERT training
# Usage: ./launch_modernbert_training.sh

set -e

echo "Starting ModernBERT Fine-Grained Reward Model Training..."

# Check if we're in the right directory
if [ ! -f "src/reward_modeling/run_fg_rm_six_modernbert.py" ]; then
    echo "Error: Please run this script from the project root directory"
    exit 1
fi

# Set environment variables
export TOKENIZERS_PARALLELISM=false
export WANDB_PROJECT=RM
export WANDB_SILENT=true
export CUDA_LAUNCH_BLOCKING=1

# Navigate to src directory
cd src

# Check GPU availability
if ! nvidia-smi > /dev/null 2>&1; then
    echo "Error: NVIDIA GPU not detected"
    exit 1
fi

echo "GPU Status:"
nvidia-smi --query-gpu=name,memory.total,memory.used --format=csv,noheader,nounits

# Run training
echo "Starting training with ModernBERT..."
bash reward_modeling/rpe_train_fact_rm_modernbert_sixinone.sh

echo "Training completed!"
EOF

chmod +x ../launch_modernbert_training.sh

# Step 9: Create verification script
print_header "Creating verification script..."
cat > ../run_modernbert_verification.sh << 'EOF'
#!/bin/bash

# Verification script for ModernBERT models
# Usage: ./run_modernbert_verification.sh

set -e

echo "Starting ModernBERT Model Verification..."

# Check if we're in the right directory
if [ ! -f "src/verification/verify_modernbert.py" ]; then
    echo "Error: Please run this script from the project root directory"
    exit 1
fi

# Set environment variables
export TOKENIZERS_PARALLELISM=false

# Navigate to src directory
cd src

# Run verification
echo "Running verification with ModernBERT models..."
python3 verification/verify_modernbert.py

echo "Verification completed!"
EOF

chmod +x ../run_modernbert_verification.sh

# Step 10: Create README for ModernBERT adaptation
print_header "Creating ModernBERT README..."
cat > ../README_ModernBERT.md << 'EOF'
# FG-PRM ModernBERT Adaptation

This is the complete adaptation of the FG-PRM (Fine-Grained Process Reward Model) project to use ModernBERT-base instead of Longformer and Llama-3.

## Key Changes

### Model Architecture
- **Original**: Longformer-base-4096 (4,096 token context)
- **New**: ModernBERT-base (8,192 token context, 149M parameters)

### Performance Improvements
- **Longer Context**: 8,192 tokens vs 4,096 tokens
- **Modern Architecture**: RoPE, Flash Attention, Local-Global attention
- **Better Efficiency**: Optimized for A100 GPUs
- **State-of-the-art Results**: Superior performance on NLU tasks

## Quick Start

### 1. Installation
```bash
# Run the complete setup (installs all dependencies)
./setup_modernbert_training.sh
```

### 2. Training
```bash
# Launch ModernBERT training
./launch_modernbert_training.sh
```

### 3. Verification
```bash
# Run model verification
./run_modernbert_verification.sh
```

## File Structure

### New Files
- `src/reward_modeling/my_modernbert.py` - ModernBERT model implementation
- `src/reward_modeling/run_fg_rm_six_modernbert.py` - Training script for ModernBERT
- `src/reward_modeling/rpe_train_fact_rm_modernbert_sixinone.sh` - Training shell script
- `src/verification/verify_modernbert.py` - Verification script
- `src/verification/reward_modernbert.py` - Reward model for verification
- `install_modernbert_dependencies.sh` - Dependency installation
- `setup_modernbert_training.sh` - Complete setup script

### Modified Files
- `src/config.yml` - Added ModernBERT model configurations

## Training Configuration

### Optimized for A100 GPU
- **Batch Size**: 8 per device
- **Gradient Accumulation**: 2 steps
- **Sequence Length**: 2,048 tokens (can be increased to 8,192)
- **Precision**: bfloat16
- **Flash Attention**: Enabled for memory efficiency

### Training Parameters
- **Learning Rate**: 5e-6
- **Weight Decay**: 0.001
- **Warmup Ratio**: 0.1
- **Epochs**: 50
- **Evaluation Strategy**: Every epoch

## Hardware Requirements

- **GPU**: NVIDIA A100 (recommended) or V100
- **Memory**: 40GB+ GPU memory for optimal performance
- **CUDA**: 12.1+
- **Python**: 3.8+

## Dependencies

### Core Requirements
- PyTorch 2.0+ with CUDA support
- Transformers 4.48.0+ (for ModernBERT support)
- Flash Attention 2
- Accelerate
- Datasets
- Evaluate

### Optional but Recommended
- Weights & Biases (wandb) for experiment tracking
- spaCy for text processing

## Model Performance

ModernBERT offers several advantages over Longformer:

1. **Longer Context**: 8,192 vs 4,096 tokens
2. **Better Architecture**: Modern attention mechanisms
3. **Improved Efficiency**: Flash Attention and optimizations
4. **State-of-the-art Results**: Superior performance on benchmarks

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**
   - Reduce batch size in training script
   - Reduce sequence length
   - Enable gradient checkpointing

2. **ModernBERT Not Found**
   - Ensure Transformers >= 4.48.0
   - Check internet connection for model download

3. **Flash Attention Issues**
   - Install with: `pip install flash-attn --no-build-isolation`
   - Ensure CUDA compatibility

## Support

For issues specific to this ModernBERT adaptation, please check:
1. GPU compatibility and memory
2. Dependency versions
3. Training data availability
4. Model checkpoint paths

Original project: Fine-grained Hallucination Mitigation and Detection in Language Model Reasoning
EOF

cd ..

print_status "Setup completed successfully!"
print_status ""
print_status "=========================================="
print_status "Next Steps:"
print_status "1. Review the README_ModernBERT.md file"
print_status "2. Ensure training data is available"
print_status "3. Run: ./launch_modernbert_training.sh"
print_status "=========================================="
print_status ""
print_status "Files created:"
print_status "- launch_modernbert_training.sh (training launcher)"
print_status "- run_modernbert_verification.sh (verification runner)"
print_status "- README_ModernBERT.md (documentation)"
print_status ""
print_status "Training will save models to: models/prm_modernbert_sixinone"
