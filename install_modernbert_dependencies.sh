#!/bin/bash

# FG-PRM ModernBERT Installation Script
# Comprehensive dependency installation for SSH container environment with A100 GPU
# Optimized for ModernBERT-base model training

set -e

echo "=========================================="
echo "FG-PRM ModernBERT Installation Script"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running in container/SSH environment
print_status "Checking environment..."
if [ -n "$SSH_CLIENT" ] || [ -n "$SSH_TTY" ]; then
    print_status "Detected SSH environment"
elif [ -f /.dockerenv ]; then
    print_status "Detected Docker container environment"
else
    print_warning "Not detected as container/SSH environment, proceeding anyway"
fi

# Check for CUDA/GPU availability
print_status "Checking CUDA availability..."
if command -v nvidia-smi &> /dev/null; then
    print_status "NVIDIA GPU detected:"
    nvidia-smi --query-gpu=name,memory.total --format=csv,noheader,nounits
else
    print_error "NVIDIA GPU not detected. This script is optimized for A100 GPU training."
    exit 1
fi

# Update system packages
print_status "Updating system packages..."
if command -v apt-get &> /dev/null; then
    sudo apt-get update
    sudo apt-get install -y \
        python3-dev \
        python3-pip \
        git \
        wget \
        curl \
        build-essential \
        software-properties-common
elif command -v yum &> /dev/null; then
    sudo yum update -y
    sudo yum install -y \
        python3-devel \
        python3-pip \
        git \
        wget \
        curl \
        gcc \
        gcc-c++ \
        make
else
    print_warning "Package manager not detected. Please install dependencies manually."
fi

# Check Python version
print_status "Checking Python version..."
python_version=$(python3 --version 2>&1 | awk '{print $2}')
print_status "Python version: $python_version"

if python3 -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)"; then
    print_status "Python version is compatible (>=3.8)"
else
    print_error "Python 3.8+ is required. Current version: $python_version"
    exit 1
fi

# Upgrade pip
print_status "Upgrading pip..."
python3 -m pip install --upgrade pip

# Install PyTorch with CUDA support
print_status "Installing PyTorch with CUDA support..."
python3 -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121

# Install Transformers with ModernBERT support
print_status "Installing Transformers (>=4.48.0 for ModernBERT support)..."
python3 -m pip install "transformers>=4.48.0"

# Install Flash Attention for optimal performance
print_status "Installing Flash Attention 2..."
python3 -m pip install flash-attn --no-build-isolation

# Install other core dependencies
print_status "Installing core ML dependencies..."
python3 -m pip install \
    datasets \
    evaluate \
    accelerate \
    wandb \
    scipy \
    numpy \
    scikit-learn \
    pandas \
    tqdm \
    pyyaml \
    nltk \
    spacy

# Install additional dependencies for the project
print_status "Installing project-specific dependencies..."
python3 -m pip install \
    peft \
    bitsandbytes \
    safetensors

# Download spaCy model
print_status "Downloading spaCy English model..."
python3 -m spacy download en_core_web_sm

# Verify installations
print_status "Verifying installations..."

# Test PyTorch CUDA
python3 -c "
import torch
print(f'PyTorch version: {torch.__version__}')
print(f'CUDA available: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'CUDA version: {torch.version.cuda}')
    print(f'GPU count: {torch.cuda.device_count()}')
    for i in range(torch.cuda.device_count()):
        print(f'GPU {i}: {torch.cuda.get_device_name(i)}')
"

# Test Transformers and ModernBERT
python3 -c "
import transformers
print(f'Transformers version: {transformers.__version__}')
try:
    from transformers import ModernBertModel, ModernBertTokenizer
    print('ModernBERT support: Available')
except ImportError as e:
    print(f'ModernBERT support: Not available - {e}')
"

# Test Flash Attention
python3 -c "
try:
    import flash_attn
    print(f'Flash Attention version: {flash_attn.__version__}')
except ImportError:
    print('Flash Attention: Not available (optional but recommended)')
"

# Create models directory
print_status "Creating models directory..."
mkdir -p models

# Set up environment variables
print_status "Setting up environment variables..."
cat >> ~/.bashrc << 'EOF'

# FG-PRM ModernBERT Environment Variables
export TOKENIZERS_PARALLELISM=false
export WANDB_PROJECT=RM
export WANDB_SILENT=true
export CUDA_LAUNCH_BLOCKING=1
EOF

print_status "Installation completed successfully!"
print_status "Please run 'source ~/.bashrc' to load environment variables."
print_status ""
print_status "Next steps:"
print_status "1. Navigate to the src directory: cd src"
print_status "2. Run the training script: bash reward_modeling/rpe_train_fact_rm_modernbert_sixinone.sh"
print_status ""
print_status "For optimal performance on A100 GPU:"
print_status "- The script is configured with batch_size=8 and gradient_accumulation_steps=2"
print_status "- Max sequence length is set to 2048 tokens (ModernBERT supports up to 8192)"
print_status "- Flash Attention 2 is enabled for memory efficiency"
