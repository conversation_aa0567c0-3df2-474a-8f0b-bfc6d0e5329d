"""
ModernBERT model implementation for token classification.
Adapted from the original my_longformer.py for fine-grained reward modeling.
"""

import torch
import torch.nn as nn
from torch.nn import CrossEntropyLoss
from transformers import (
    ModernBertModel,
    ModernBertPreTrainedModel,
    ModernBertConfig,
)
from transformers.modeling_outputs import TokenClassifierOutput
from typing import Optional, Tuple, Union


class ModernBertForTokenClassification(ModernBertPreTrainedModel):
    """
    ModernBERT model for token classification tasks.
    
    This model is adapted for fine-grained reward modeling, specifically for
    hallucination detection in language model reasoning.
    """
    
    def __init__(self, config: ModernBertConfig):
        super().__init__(config)
        self.num_labels = config.num_labels
        
        # ModernBERT backbone
        self.modernbert = ModernBertModel(config, add_pooling_layer=False)
        
        # Classification head
        classifier_dropout = (
            config.classifier_dropout if config.classifier_dropout is not None 
            else config.hidden_dropout_prob
        )
        self.dropout = nn.Dropout(classifier_dropout)
        self.classifier = nn.Linear(config.hidden_size, config.num_labels)
        
        # Initialize weights
        self.post_init()
    
    def forward(
        self,
        input_ids: Optional[torch.LongTensor] = None,
        attention_mask: Optional[torch.Tensor] = None,
        token_type_ids: Optional[torch.LongTensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        head_mask: Optional[torch.Tensor] = None,
        inputs_embeds: Optional[torch.FloatTensor] = None,
        labels: Optional[torch.LongTensor] = None,
        output_attentions: Optional[bool] = None,
        output_hidden_states: Optional[bool] = None,
        return_dict: Optional[bool] = None,
    ) -> Union[Tuple[torch.Tensor], TokenClassifierOutput]:
        """
        Forward pass for token classification.
        
        Args:
            input_ids: Input token IDs
            attention_mask: Attention mask
            token_type_ids: Token type IDs (not used in ModernBERT)
            position_ids: Position IDs
            head_mask: Head mask
            inputs_embeds: Input embeddings
            labels: Labels for computing loss
            output_attentions: Whether to output attentions
            output_hidden_states: Whether to output hidden states
            return_dict: Whether to return a dict
            
        Returns:
            TokenClassifierOutput or tuple with logits and optional loss
        """
        return_dict = return_dict if return_dict is not None else self.config.use_return_dict
        
        # Get ModernBERT outputs
        outputs = self.modernbert(
            input_ids=input_ids,
            attention_mask=attention_mask,
            position_ids=position_ids,
            head_mask=head_mask,
            inputs_embeds=inputs_embeds,
            output_attentions=output_attentions,
            output_hidden_states=output_hidden_states,
            return_dict=return_dict,
        )
        
        # Get sequence output (last hidden state)
        sequence_output = outputs[0]
        
        # Apply dropout and classification
        sequence_output = self.dropout(sequence_output)
        logits = self.classifier(sequence_output)
        
        loss = None
        if labels is not None:
            loss_fct = CrossEntropyLoss()
            loss = loss_fct(logits.view(-1, self.num_labels), labels.view(-1))
        
        if not return_dict:
            output = (logits,) + outputs[2:]
            return ((loss,) + output) if loss is not None else output
        
        return TokenClassifierOutput(
            loss=loss,
            logits=logits,
            hidden_states=outputs.hidden_states,
            attentions=outputs.attentions,
        )


class ModernBertForTokenMultiClassification(ModernBertPreTrainedModel):
    """
    ModernBERT model for multi-class token classification.
    
    This variant supports multiple classification heads for different
    types of hallucination detection (6 different error types).
    """
    
    def __init__(self, config: ModernBertConfig):
        super().__init__(config)
        self.num_labels = config.num_labels
        self.num_classes = 6  # Number of hallucination types
        
        # ModernBERT backbone
        self.modernbert = ModernBertModel(config, add_pooling_layer=False)
        
        # Dropout
        classifier_dropout = (
            config.classifier_dropout if config.classifier_dropout is not None 
            else config.hidden_dropout_prob
        )
        self.dropout = nn.Dropout(classifier_dropout)
        
        # Multiple classification heads (one for each hallucination type)
        self.classifiers = nn.ModuleList([
            nn.Linear(config.hidden_size, config.num_labels) 
            for _ in range(self.num_classes)
        ])
        
        # Initialize weights
        self.post_init()
    
    def forward(
        self,
        input_ids: Optional[torch.LongTensor] = None,
        attention_mask: Optional[torch.Tensor] = None,
        token_type_ids: Optional[torch.LongTensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        head_mask: Optional[torch.Tensor] = None,
        inputs_embeds: Optional[torch.FloatTensor] = None,
        labels: Optional[torch.LongTensor] = None,
        output_attentions: Optional[bool] = None,
        output_hidden_states: Optional[bool] = None,
        return_dict: Optional[bool] = None,
    ) -> Union[Tuple[torch.Tensor], TokenClassifierOutput]:
        """
        Forward pass for multi-class token classification.
        """
        return_dict = return_dict if return_dict is not None else self.config.use_return_dict
        
        # Get ModernBERT outputs
        outputs = self.modernbert(
            input_ids=input_ids,
            attention_mask=attention_mask,
            position_ids=position_ids,
            head_mask=head_mask,
            inputs_embeds=inputs_embeds,
            output_attentions=output_attentions,
            output_hidden_states=output_hidden_states,
            return_dict=return_dict,
        )
        
        # Get sequence output
        sequence_output = outputs[0]
        sequence_output = self.dropout(sequence_output)
        
        # Apply all classifiers
        logits_list = []
        for classifier in self.classifiers:
            logits_list.append(classifier(sequence_output))
        
        # Stack logits from all classifiers
        logits = torch.stack(logits_list, dim=-1)  # [batch, seq_len, num_labels, num_classes]
        
        loss = None
        if labels is not None:
            loss_fct = CrossEntropyLoss()
            # Compute loss for each classifier
            total_loss = 0
            for i, classifier_logits in enumerate(logits_list):
                class_loss = loss_fct(
                    classifier_logits.view(-1, self.num_labels), 
                    labels.view(-1)
                )
                total_loss += class_loss
            loss = total_loss / self.num_classes
        
        if not return_dict:
            output = (logits,) + outputs[2:]
            return ((loss,) + output) if loss is not None else output
        
        return TokenClassifierOutput(
            loss=loss,
            logits=logits,
            hidden_states=outputs.hidden_states,
            attentions=outputs.attentions,
        )
