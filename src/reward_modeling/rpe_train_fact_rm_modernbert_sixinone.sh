#!/bin/bash
set -e

# ModernBERT Fine-Grained Reward Model Training Script
# Optimized for single A100 GPU training
# Adapted from rpe_train_fact_rm_longformer_sixinone.sh

gpu_num=$(echo $CUDA_VISIBLE_DEVICES | awk -F ',' '{print NF}')

# If CUDA_VISIBLE_DEVICES is not set, assume single GPU
if [ -z "$CUDA_VISIBLE_DEVICES" ]; then
    gpu_num=1
fi

echo "Training ModernBERT reward model with $gpu_num GPU(s)"
echo "Using ModernBERT-base model: answerdotai/ModernBERT-base"

# Train reward model for PRM using ModernBERT
torchrun --nproc_per_node $gpu_num --standalone --nnodes=1 ./reward_modeling/run_fg_rm_six_modernbert.py \
                --model_name_or_path answerdotai/ModernBERT-base \
                --train_file ../data/hallucination_sample/orm_sample_cg-h/synthetic_sixinone_train.json \
                --validation_file ../data/hallucination_sample/orm_sample_cg-h/synthetic_sixinone_dev.json \
                --test_file ../data/hallucination_sample/orm_sample_cg-h/synthetic_sixinone_test.json \
                --output_dir ../models/prm_modernbert_sixinone \
                --do_train \
                --do_eval \
                --do_predict \
                --bf16 \
                --num_train_epochs 50 \
                --per_device_train_batch_size 8 \
                --per_device_eval_batch_size 8 \
                --gradient_accumulation_steps 2 \
                --evaluation_strategy epoch \
                --logging_strategy epoch \
                --save_strategy epoch \
                --load_best_model_at_end \
                --metric_for_best_model overall_accuracy \
                --max_seq_length 2048 \
                --report_to wandb \
                --save_total_limit 2 \
                --learning_rate 0.000005 \
                --weight_decay 0.001 \
                --warmup_ratio 0.1 \
                --dataloader_num_workers 4 \
                --remove_unused_columns False \
                --overwrite_output_dir

echo "Training completed successfully!"
echo "Model saved to: ../models/prm_modernbert_sixinone"
