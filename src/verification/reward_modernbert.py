import json
import os
from typing import Optional, List, Iterable, Dict, Any, <PERSON><PERSON>
import torch, spacy
import torch.nn.functional as F
from transformers import AutoTokenizer, AutoConfig
import numpy as np
import logging
import re

from fgrlhf.reward import BasicReward
from fgrlhf.reward_utils import split_text_to_subsentences, split_text_to_sentences

from my_modernbert import ModernBertForTokenClassification

logging.basicConfig(level=logging.ERROR)

class FactualityReward(BasicReward):
    def __init__(self, 
                 tokenizer, 
                 f_reward_model_path: str,
                 positive_reward: float = 0.5,
                 negative_reward: float = -0.5,
                 sep: str = '</s>',
                 ):
        super().__init__()
        self.tokenizer = tokenizer
        self.positive_reward = positive_reward
        self.negative_reward = negative_reward
        self.sep = sep
        
        # Load ModernBERT reward model
        config = AutoConfig.from_pretrained(f_reward_model_path)
        self.f_reward_model = ModernBertForTokenClassification.from_pretrained(
            f_reward_model_path,
            config=config
        )
        self.f_reward_model.eval()
        
        # Load spaCy model for sentence splitting
        try:
            self.nlp = spacy.load("en_core_web_sm")
        except OSError:
            print("spaCy English model not found. Please install it with: python -m spacy download en_core_web_sm")
            raise
    
    def process_one_generation(self, gen_text: str, policy_text_len: int):
        """Process a single generation to prepare it for reward model evaluation."""
        
        # Split text into sentences
        sentences = split_text_to_sentences(gen_text, self.nlp)
        
        # Calculate sentence end indices
        sentence_end_indices = []
        current_pos = 0
        
        for sentence in sentences:
            # Find the end position of this sentence in the original text
            sentence_end = gen_text.find(sentence, current_pos) + len(sentence)
            sentence_end_indices.append(sentence_end)
            current_pos = sentence_end
        
        # Create reward input with separators
        reward_sentences = [f"{sent} {self.sep}" for sent in sentences]
        reward_input = ' '.join(reward_sentences)
        
        # Ensure indices don't exceed policy text length
        sentence_end_indices = [min(item, policy_text_len-1) for item in sentence_end_indices]
        
        return reward_input, sentence_end_indices
    
    def get_reward(self, 
                   prompts_input_ids: torch.tensor, 
                   prompts_attention_mask: torch.tensor, 
                   generated_input_ids: torch.tensor,  # (B, output_len)
                   generated_attention_mask: torch.tensor,  # (B, output_len)
                   generated_texts: List[str],
                   metadata=None,
                   ):
        """Get reward scores for generated texts."""
        
        batch_f_reward_inputs = []
        batch_sentence_end_indices = []
        
        # Get the length of generated outputs
        policy_inputs_lens = torch.sum(generated_attention_mask, dim=1).tolist()

        for batch_idx, (meta, gen_text) in enumerate(zip(metadata, generated_texts)):
            reward_input, sentence_end_indices = self.process_one_generation(
                gen_text, policy_inputs_lens[batch_idx]
            )

            # Input for the factual reward model
            f_reward_input = f"{meta['prompt']} answer: {reward_input}"
            batch_f_reward_inputs.append(f_reward_input)
            
            # The indices of sentence ends for the policy model output
            batch_sentence_end_indices.append(sentence_end_indices)

        # Tokenize inputs for reward model
        f_reward_tokenized = self.tokenizer(
            batch_f_reward_inputs,
            return_tensors='pt',
            padding=True,
            truncation=True,
            max_length=2048,  # ModernBERT supports up to 8192, but 2048 is sufficient for most cases
        )
        
        # Get reward model predictions
        with torch.no_grad():
            f_reward_outputs = self.f_reward_model(
                input_ids=f_reward_tokenized['input_ids'],
                attention_mask=f_reward_tokenized['attention_mask'],
            )
            f_reward_logits = f_reward_outputs.logits  # (batch_size, seq_len, num_labels)

        # Process rewards for each batch item
        batch_rewards = []
        batch_raw_rewards = []
        batch_n_sentences = []
        batch_n_corrects = []

        for batch_idx in range(len(generated_texts)):
            sentence_end_indices = batch_sentence_end_indices[batch_idx]
            logits = f_reward_logits[batch_idx]  # (seq_len, num_labels)
            
            # Extract rewards at sentence boundaries
            sentence_rewards = []
            raw_rewards = []
            
            for end_idx in sentence_end_indices:
                if end_idx < logits.shape[0]:
                    sentence_logit = logits[end_idx]  # (num_labels,)
                    raw_rewards.append(sentence_logit)
                    
                    # Apply softmax and get reward score
                    probs = F.softmax(sentence_logit, dim=0)
                    # Assuming label 0 is "O" (no error) and label 2 is "ERR" (error)
                    if len(probs) > 2:
                        reward_score = self.positive_reward if probs[0] > probs[2] else self.negative_reward
                    else:
                        reward_score = self.positive_reward if probs[0] > 0.5 else self.negative_reward
                    
                    sentence_rewards.append(reward_score)
            
            batch_rewards.append(sentence_rewards)
            batch_raw_rewards.append(raw_rewards)
            batch_n_sentences.append(len(sentence_end_indices))
            batch_n_corrects.append(sum(1 for r in sentence_rewards if r > 0))

        return {
            'factuality_rewards': batch_rewards,
            'raw_rewards': batch_raw_rewards,
            'n_sentences': batch_n_sentences,
            'n_corrects': batch_n_corrects,
        }


class FactualityCompactReward(FactualityReward):
    """Compact version of the factuality reward model."""
    
    def __init__(self, 
                 tokenizer, 
                 f_reward_model_path: str,
                 positive_reward: float = 0.5,
                 negative_reward: float = -0.5,
                 sep: str = '</s>',
                 ):
        super().__init__(tokenizer, f_reward_model_path, positive_reward, negative_reward, sep)
    
    def process_one_generation(self, gen_text: str, policy_text_len: int):
        """Compact processing - use subsentences instead of full sentences."""
        
        # Split text into subsentences for more fine-grained analysis
        subsentences = split_text_to_subsentences(gen_text, self.nlp)
        
        # Calculate subsentence end indices
        sentence_end_indices = []
        current_pos = 0
        
        for subsentence in subsentences:
            # Find the end position of this subsentence in the original text
            subsentence_end = gen_text.find(subsentence, current_pos) + len(subsentence)
            sentence_end_indices.append(subsentence_end)
            current_pos = subsentence_end
        
        # Create reward input with separators
        reward_sentences = [f"{sent} {self.sep}" for sent in subsentences]
        reward_input = ' '.join(reward_sentences)
        
        # Ensure indices don't exceed policy text length
        sentence_end_indices = [min(item, policy_text_len-1) for item in sentence_end_indices]
        
        return reward_input, sentence_end_indices
